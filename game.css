body {
    margin: 0;
    padding: 0;
    font-family: 'Arial', sans-serif;
    overflow: hidden;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

.background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('images/backdrop stars 2-01.png'); /* Replace with your image path */
    background-size: cover;
    background-position: center;
    filter: brightness(0.7);
    z-index: -1;
}


.logo {
    position: absolute;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    width: 1500px;
    height: 350px;
    background-image: url('images/Daka.png'); 
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}

.girl-character {
    position: absolute;
    bottom: 20px;
    left: 50%;                   /* Center horizontally */
    transform: translateX(-50%);  /* Adjust for exact centering */
    width: 150px;
    height: 300px;
    background-image: url('images/character design female.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    z-index: 1;                  /* Ensure she appears above background */
}
#startButton {
    background: none;
    border: none;
    color: yellow; /* or whatever your glitter color is */
    font-family: 'Press Start 2P', cursive;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    /* Add your glitter effect styles here */
    text-shadow: 0 0 10px #ff0, 0 0 20px #ff0;
    animation: glitter 1s linear infinite;
}

/* Keep your existing glitter animation */
@keyframes glitter {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}
/* Base Styles */




/* Start Screen */
#startScreen {
    position: absolute;
    width: 100%;
    height: 100%;
    background: url('images/BAR 1.png') center/cover;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    margin: 0;
    padding: 0;
    font-family: 'Press Start 2P', cursive;
    color: white;
    height: 100vh;
    overflow: hidden;
    background-color: black;
}

#startButton {
    background: none;
    border: none;
    color: yellow;
    font-size: 24px;
    cursor: pointer;
    margin-top: 40px;
    padding: 0;
    font-family: inherit;
    text-shadow: 0 0 10px #ff0, 0 0 20px #ff0;
    animation: glitter 1s linear infinite;
    font-family: 'Press Start 2P', cursive;
    
}


/* Main Menu Screen */
#mainMenu {
    position: absolute;
    width: 100%;
    height: 100%;
    background: url('images/BAR2.png') center/cover;
    display: none;
    flex-direction: column;
    justify-content: flex-end;
    align-items: center;
    padding-bottom: 50px;
}

.menu-buttons {
    display: flex;
    gap: 20px;
    margin-bottom: 30px;
}

.menu-button {
    background: url('images/BAR3.png') center/contain no-repeat;
    border: none;
    width: 200px;
    height: 60px;
    color: white;
    font-size: 10px;
    cursor: pointer;
    padding-top: 25px;
    text-align: center;
    font-family: inherit;
}

.menu-button:hover {
    background-image: url('images/BAR4.png');
}

/* Animations */
@keyframes glitter {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}