function selectCharacter(imageFile) {
  // Store selected image
  localStorage.setItem('selectedCharacter', imageFile);

  // Create and show popup
  const popup = document.createElement('div');
  popup.className = 'popup';
  popup.textContent = 'Character selected!';
  document.body.appendChild(popup);

  // Show popup
  popup.style.display = 'block';

  // Hide popup and redirect after delay
  setTimeout(function() {
    popup.style.display = 'none';
    window.location.href = 'mainPlayer.html';
  }, 1500);
}

// Test function to clear localStorage (for debugging)
function clearStorage() {
  localStorage.clear();
  location.reload();
}

// Hide saved character from selection when page loads
document.addEventListener('DOMContentLoaded', function() {
  const savedCharacter = localStorage.getItem('savedCharacter');

  // Determine which character is currently saved (either from localStorage or default Frame 11)
  let currentSavedCharacter;
  if (savedCharacter) {
    currentSavedCharacter = savedCharacter.split('/').pop();
  } else {
    // If no saved character, Frame 11 is the default saved character
    currentSavedCharacter = 'Frame 11.png';
  }

  // Hide the card that matches the current saved character
  const cards = document.querySelectorAll('.card');
  cards.forEach(card => {
    const characterPath = card.getAttribute('data-character');
    if (characterPath) {
      const cardCharacterFile = characterPath.split('/').pop();
      if (cardCharacterFile === currentSavedCharacter) {
        card.classList.add('hidden');
      }
    }
  });

  // Force hide Frame 11 if no saved character (ensure default behavior)
  if (!savedCharacter) {
    const frame11Card = document.querySelector('[data-character="img/Frame 11.png"]');
    if (frame11Card) {
      frame11Card.classList.add('hidden');
    }
  }
});






