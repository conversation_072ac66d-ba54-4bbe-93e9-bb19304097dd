<!-- nonplayer.html -->
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Choose Character</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Press+Start+2P&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="css/nonplayer-styles.css">
</head>
<body>

  <div class="background-wrapper">
    <img src="img/non-player.png" class="bg" alt="Background">

    <div class="card-group">
      <div class="card" data-character="img/Frame2.png" onclick="selectCharacter('img/Frame2.png')"><img src="img/Frame2.png" alt="Card 2"></div>
      <div class="card" data-character="img/Frame3.png" onclick="selectCharacter('img/Frame3.png')"><img src="img/Frame3.png" alt="Card 3"></div>
      <div class="card" data-character="img/Frame4.png" onclick="selectCharacter('img/Frame4.png')"><img src="img/Frame4.png" alt="Card 4"></div>
      <div class="card" data-character="img/Frame5.png" onclick="selectCharacter('img/Frame5.png')"><img src="img/Frame5.png" alt="Card 5"></div>
      <div class="card" data-character="img/Frame6.png" onclick="selectCharacter('img/Frame6.png')"><img src="img/Frame6.png" alt="Card 6"></div>
      <div class="card" data-character="img/Frame 11.png" onclick="selectCharacter('img/Frame 11.png')"><img src="img/Frame 11.png" alt="Card 11"></div>
    </div>
  </div>

  <script src="js/nonplayer-script.js"></script>
</body>
</html>
