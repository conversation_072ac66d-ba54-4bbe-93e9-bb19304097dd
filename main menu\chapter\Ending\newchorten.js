// Game Canvas Setup
const canvas = document.getElementById("gameCanvas");
const ctx = canvas.getContext("2d");
const messageDiv = document.getElementById("message");
const congratsDiv = document.getElementById("congrats-message");
const closeButton = document.getElementById("close-message");

// Game Images - Start with same images as chorten for seamless transition
const images = {
    background: loadImage("landchorten.png"), // Same as chorten initially
    newBackground: loadImage("3ppl.png"), // New background to transition to
    girlWalk: loadImage("girlwalk.png"), // Same character as chorten initially
    womanWalk: loadImage("female character1.png"), // New character to transition to
    dorji: loadImage("dorji.png"),
    key: loadImage("key.png"),
    lamp: loadImage("lamp.png"),
    temple: loadImage("chorten.png")
};

// Game State - Updated to woman character
const woman = {  // Changed from girl to woman
    x: 1000,  // Start at same position where girl stopped in chorten
    y: 470,   // Match the Y position from chorten (was 400)
    width: 80,
    height: 190,
    speed: 2,  // Smooth movement speed
    targetX: 1100,  // Move further right
    targetY: 400,   // Move up slightly
    isMoving: true,
    useGirlImage: true  // Start with girl image for seamless transition
};

// Scene transition state
const sceneTransition = {
    phase: 'initial', // 'initial', 'moving', 'transforming', 'complete'
    backgroundOpacity: 0, // For new background fade-in
    characterTransition: 0, // 0 = girl, 1 = woman
    templeOpacity: 0, // For temple fade-in
    transitionSpeed: 0.02
};

const temple = {
    x: 1050,
    y: 150,
    width: 200,
    height: 200
};

// Initialize Game
function init() {
    // Use same canvas dimensions as chorten for seamless transition
    canvas.width = images.background.width;
    canvas.height = images.background.height;

    closeButton.addEventListener("click", () => {
        messageDiv.classList.add("hidden");
        showCongratulations();
    });

    // Start scene transition after a brief moment
    setTimeout(() => {
        sceneTransition.phase = 'moving';
    }, 100);

    gameLoop();
}

// Show Congratulations
function showCongratulations() {
    congratsDiv.classList.remove("hidden");
    
    // Glitter burst after 2 seconds
    setTimeout(() => {
        createGlitterBurst(temple);
    }, 2000);
}

// Create Glitter Burst Effect
function createGlitterBurst(source) {
    const centerX = source.x + source.width/2;
    const centerY = source.y + source.height/2;
    
    for (let i = 0; i < 100; i++) {
        setTimeout(() => {
            const particle = document.createElement("div");
            particle.className = "glitter-particle";
            
            // Random direction and distance
            const angle = Math.random() * Math.PI * 2;
            const distance = 50 + Math.random() * 100;
            const tx = Math.cos(angle) * distance;
            const ty = Math.sin(angle) * distance;
            
            particle.style.setProperty("--tx", `${tx}px`);
            particle.style.setProperty("--ty", `${ty}px`);
            particle.style.left = `${centerX}px`;
            particle.style.top = `${centerY}px`;
            
            // Random color variation
            const hue = 50 + Math.random() * 10;
            particle.style.backgroundColor = `hsl(${hue}, 100%, 50%)`;
            
            document.body.appendChild(particle);
            
            // Remove after animation
            setTimeout(() => {
                particle.remove();
            }, 2000);
        }, Math.random() * 200);
    }
}

// Main Game Loop - Updated for smooth scene transition
function gameLoop() {
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Update scene transition
    updateSceneTransition();

    // Draw backgrounds with transition
    drawBackgrounds();

    // Draw temple with fade-in
    if (sceneTransition.templeOpacity > 0) {
        ctx.save();
        ctx.globalAlpha = sceneTransition.templeOpacity;
        ctx.drawImage(images.temple, temple.x, temple.y);
        ctx.restore();
    }

    // Update woman position with smooth movement
    if (woman.isMoving) {
        // Move towards target position
        const deltaX = woman.targetX - woman.x;
        const deltaY = woman.targetY - woman.y;
        const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

        if (distance > 2) {
            // Normalize direction and apply speed
            woman.x += (deltaX / distance) * woman.speed;
            woman.y += (deltaY / distance) * woman.speed;
        } else {
            // Snap to final position and stop
            woman.x = woman.targetX;
            woman.y = woman.targetY;
            woman.isMoving = false;
            // Start transforming scene when movement is complete
            if (sceneTransition.phase === 'moving') {
                sceneTransition.phase = 'transforming';
            }
        }
    }

    // Draw character with transition
    drawCharacter();

    requestAnimationFrame(gameLoop);
}

function updateSceneTransition() {
    switch (sceneTransition.phase) {
        case 'moving':
            // Character is moving, no visual changes yet
            break;

        case 'transforming':
            // Gradually transition backgrounds and character
            sceneTransition.backgroundOpacity = Math.min(1, sceneTransition.backgroundOpacity + sceneTransition.transitionSpeed);
            sceneTransition.characterTransition = Math.min(1, sceneTransition.characterTransition + sceneTransition.transitionSpeed);
            sceneTransition.templeOpacity = Math.min(1, sceneTransition.templeOpacity + sceneTransition.transitionSpeed);

            if (sceneTransition.backgroundOpacity >= 1) {
                sceneTransition.phase = 'complete';
            }
            break;
    }
}

function drawBackgrounds() {
    // Draw original background
    ctx.drawImage(images.background, 0, 0);

    // Draw new background with fade-in
    if (sceneTransition.backgroundOpacity > 0) {
        ctx.save();
        ctx.globalAlpha = sceneTransition.backgroundOpacity;
        ctx.drawImage(images.newBackground, 0, 0);
        ctx.restore();
    }
}

function drawCharacter() {
    // Choose which character image to use based on transition
    let characterImage;
    if (sceneTransition.characterTransition < 0.5) {
        characterImage = images.girlWalk;
    } else {
        characterImage = images.womanWalk;
    }

    ctx.drawImage(
        characterImage,
        woman.x,
        woman.y,
        woman.width,
        woman.height
    );
}

// Helper function to load images
function loadImage(src) {
    const img = new Image();
    img.src = src;
    return img;
}

// Start game when images load
let loadedImages = 0;
Object.values(images).forEach(img => {
    img.onload = () => {
        loadedImages++;
        if (loadedImages === Object.keys(images).length) {
            init();
        }
    };
});
