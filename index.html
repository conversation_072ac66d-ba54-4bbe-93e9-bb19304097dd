<!DOCTYPE html>
<html>
  <head>
    <link rel="stylesheet" href="background_styles.css">
    <link rel="stylesheet" href="./loading.css">
    <script src="./loading.js" defer></script>
    <title>Progress Bar</title>
  </head>
<body>
<div class="container">
	<img src="./images/Daka.png" alt="AchoDaka Logo" class="logo" />
	<p class="tagline">"A Journey Beyond the stars"</p> <br><br> <br> <br>

	<img src="./images/mascot.png" alt="Mascot" class="mascot" />
	<p class="loading-text">LOADING...</p>

	<div id="progress" class="progress-bar" data-label="" style="--width: 0;"></div>
  <script>
    // script.js

// Simulate loading progress
let progress = 0;
const progressBar = document.getElementById('progress');
const loadingText = document.querySelector('.loading-text');
const container = document.querySelector('.container');

function updateProgress() {
    progress += Math.random() * 10; // Random increment for realistic loading
    if (progress > 100) progress = 100;
    
    progressBar.style.setProperty('--width', progress);
    progressBar.setAttribute('data-label', `${Math.round(progress)}%`);
    
    if (progress < 100) {
        setTimeout(updateProgress, 300); // Continue loading
    } else {
        loadingText.textContent = "LOADING COMPLETE!";
        
        // Add a slight delay before redirecting
        setTimeout(() => {
            window.location.href = "game.html"; // Change this to your target page
        }, 1000); // 1 second delay after loading completes
    }
}

// Start the loading process when page loads
window.addEventListener('DOMContentLoaded', () => {
    setTimeout(updateProgress, 500); // Small initial delay
});
 </script>
</div>
</body>

</html>