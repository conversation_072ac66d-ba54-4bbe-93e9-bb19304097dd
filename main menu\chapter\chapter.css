body {
    font-family: 'Press Start 2P', cursive, sans-serif;
    margin: 0;
    padding: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    image-rendering: pixelated;
    background-image: url('./images/background.png');
    background-size: cover;
    background-repeat: no-repeat;
    background-attachment: fixed;
}

.game-container {
    background-image: url('./images/container.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    border-radius: 0;
    border: 4px solid #555;
    box-shadow: 8px 8px 0 #111;
    padding: 20px;
    width: 1300px;
    height: 600px;
    aspect-ratio: 16 / 9;
    text-align: center;
}

h1 {
    color: #fff;
    margin-bottom: 20px;
    font-size: 24px;
    text-shadow: 4px 4px 0 #000;
    background-image: url('./images/chapter.png');
    background-size: cover;
    background-position: center;
    width: 700px;
    height: 60px;
    display: inline-block;
    color: #fff;
    font-size: 32px;
    text-shadow: 4px 4px 0 #000;
    padding: 15px 30px;
    display: inline-block;
    border: 4px solid #fff;
    box-shadow: 
        0 0 0 4px #000,
        6px 6px 0 rgba(0,0,0,0.5);
    position: relative;
    top: -95px;
    margin-bottom: 15px;
}

.levels-rack {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 40px;
}

.rope {
    width: 16px;
    height: 4px;
    background: #8B4513;
    border-radius: 2px;
    box-shadow: 2px 2px 0 #000;
}

.levels-container {
    display: flex;
    align-items: center;
    gap: 0;
}

.level-circle {
    width: 100px;
    height: 100px;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    box-shadow: 4px 4px 0 #000;
    border-radius: 50%;
    border: 4px solid #000;
    image-rendering: pixelated;
    margin: 0 20px;
    background-size: cover;
    background-position: center;
    overflow: hidden;
}

.level-circle img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: none;
}

.level-circle img[src]:not([src=""]):not([src="./images/.png"]) {
    display: block;
}

.level-circle[data-level="1"] {
    background-color: #ffffff;
}

.level-circle[data-level="2"] {
    background-color: #0066ff;
}

.level-circle[data-level="3"] {
    background-color: #6ea787;
}

.level-circle[data-level="4"] {
    background-color: #ff3300;
}

.level-circle[data-level="5"] {
    background-color: #ffcc00;
}

.level-circle:hover {
    transform: translateY(-8px) scale(1.1);
    z-index: 10;
}

.level-circle:hover::after {
    content: "CHAPTER " attr(data-level);
    position: absolute;
    bottom: -32px;
    left: 50%;
    transform: translateX(-50%);
    background-color: #000;
    color: white;
    padding: 4px 10px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
    z-index: 20;
    border: 2px solid #fff;
    white-space: nowrap;
}

.connector-rope {
    width: 20px;
    height: 4px;
    background: #8B4513;
    border-radius: 2px;
    box-shadow: 2px 2px 0 #000;
}

.level-info {
    margin-top: 40px;
    padding: 16px;
    background-color: #444;
    border-radius: 0;
    border: 4px solid #555;
    box-shadow: 4px 4px 0 #111;
}

.current-level-display {
    width: 100px;
    height: 100px;
    margin: 0 auto 16px;
    border-radius: 50%;
    border: 4px solid #000;
    transition: all 0.3s ease;
    box-shadow: 4px 4px 0 #000;
    position: relative;
}

#levelDescription {
    font-size: 12px;
    color: #eee;
    min-height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1.5;
    text-shadow: 2px 2px 0 #000;
}

@keyframes pixelPulse {
    0% { transform: translateY(0); }
    50% { transform: translateY(-4px); }
    100% { transform: translateY(0); }
}

@media (max-width: 600px) {
    .level-circle {
        width: 80px;
        height: 80px;
        margin: 0 8px;
    }
    
    .current-level-display {
        width: 70px;
        height: 70px;
    }
    
    .connector-rope {
        width: 10px;
    }
    
    h1 {
        font-size: 18px;
    }
    
    #levelDescription {
        font-size: 10px;
    }
}

/* Add these styles for locked levels */
.level-circle.locked {
    position: relative;
    filter: opacity(0.7) brightness(0.7);
    cursor: default;
}

.level-circle.locked::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 50%;
    z-index: 5;
}

.level-circle.locked::after {
    content: "🔒";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 24px;
    z-index: 10;
}

.level-circle.locked:hover {
    transform: none;
}

.level-circle.locked:hover::after {
    content: "🔒";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: transparent;
    border: none;
    padding: 0;
    font-size: 24px;
    bottom: auto;
}
