document.addEventListener('DOMContentLoaded', function() {
    const levelCircles = document.querySelectorAll('.level-circle');
    const currentLevelDisplay = document.getElementById('currentLevel');
    const levelDescription = document.getElementById('levelDescription');
    
    // Level descriptions
    const levelData = {
        1: {
            name: "THE VILLAGE'S LAMENT",
            description: 'BEGINNING OF YOUR JOURNEY. PURE AND FULL OF POTENTIAL.',
            link: './Chapter1/lhamo.html'
        },
        2: {
            name: 'THE FROZEN PEAKS',
            description: 'DIVE INTO DEEPER CHALLENGES WITH THE BLUE WATERS.',
            link: './Chapter2/house.html'
        },
        3: {
            name: 'THE FIERY ABYSS',
            description: 'GROWTH AND PROSPERITY AWAIT IN THIS STAGE.',
        },
        4: {
            name: 'VERDANT GROVE',
            description: 'FIERY TEST OF YOUR TRUE SKILLS BEGINS HERE.',
        },
        5: {
            name: 'THE UNDERWORLD DEPTHS',
            description: 'GOLDEN PINNACLE OF ACHIEVEMENT. ONLY THE WORTHY REACH HERE.',
        }
    };
    
    // Variable to track the last hovered level
    let lastHoveredLevel = null;
    
    levelCircles.forEach(circle => {
        const level = circle.getAttribute('data-level');
        const isLocked = circle.classList.contains('locked');
        
        // Skip adding hover effects for locked levels
        if (isLocked) {
            circle.addEventListener('mouseenter', () => {
                // For locked levels, just show a locked message
                levelDescription.textContent = `CHAPTER ${level}: LOCKED - COMPLETE PREVIOUS CHAPTERS TO UNLOCK`;
            });
            
            circle.addEventListener('mouseleave', () => {
                // Reset description when mouse leaves
                if (lastHoveredLevel) {
                    // Show the last hovered level description
                    levelDescription.textContent = `CHAPTER ${lastHoveredLevel}: ${levelData[lastHoveredLevel].name} - ${levelData[lastHoveredLevel].description}`;
                } else {
                    levelDescription.textContent = 'CHOOSE A CHAPTER!';
                }
            });
            return;
        }
        
        // Regular hover effects for unlocked levels
        circle.addEventListener('mouseenter', () => {
            // Get computed style from the circle
            const circleStyle = window.getComputedStyle(circle);
            const backgroundColor = circleStyle.backgroundColor;
            
            // Check if the circle has an image
            const img = circle.querySelector('img');
            const hasValidImage = img && img.src && img.src !== '' && !img.src.includes('./images/.png');
            
            // Update current level display
            if (hasValidImage) {
                currentLevelDisplay.style.backgroundImage = `url('${img.src}')`;
                currentLevelDisplay.style.backgroundSize = 'cover';
                currentLevelDisplay.style.backgroundColor = 'transparent';
            } else {
                currentLevelDisplay.style.backgroundImage = 'none';
                currentLevelDisplay.style.backgroundColor = backgroundColor;
            }
            currentLevelDisplay.style.border = '2px solid #000';
            
            // Update description
            levelDescription.textContent = `CHAPTER ${level}: ${levelData[level].name} - ${levelData[level].description}`;
            
            // Update last hovered level
            lastHoveredLevel = level;
            
            // Add pulse animation
            currentLevelDisplay.style.animation = 'pixelPulse 0.3s steps(2)';
        });
        
        circle.addEventListener('mouseleave', () => {
            // Remove pulse animation
            currentLevelDisplay.style.animation = 'none';
            currentLevelDisplay.style.filter = 'none';
            // Keep the description of the last hovered level
        });
    });
    
    // Add click event for unlocked levels
    levelCircles.forEach(circle => {
        const level = circle.getAttribute('data-level');
        const isLocked = circle.classList.contains('locked');
        
        if (!isLocked) {
            circle.addEventListener('click', () => {
                // Navigate to the chapter if it has a link
                if (levelData[level] && levelData[level].link) {
                    window.location.href = levelData[level].link;
                }
            });
            
            // Add cursor pointer style to indicate clickable
            circle.style.cursor = 'pointer';
        }
    });
    
    // Add animation for initial display
    currentLevelDisplay.style.backgroundColor = levelData[1].color;
    currentLevelDisplay.style.border = levelData[1].border;
    currentLevelDisplay.style.width = '100px';
    currentLevelDisplay.style.height = '100px';
    levelDescription.textContent = `CHOOSE A CHAPTER`;
});

// Add pixel pulse animation
const style = document.createElement('style');
style.textContent = `
    @keyframes pixelPulse {
        0% { transform: translateY(0); }
        50% { transform: translateY(-4px); }
        100% { transform: translateY(0); }
    }
`;
document.head.appendChild(style);
