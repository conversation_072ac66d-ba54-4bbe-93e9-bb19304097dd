@import url('https://fonts.googleapis.com/css2?family=Press+Start+2P&display=swap');

body, html {
  height: 100%;
  width: 100%;
  margin: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #000;
  font-family: 'Press Start 2P', cursive;
}

.background-wrapper {
  position: relative;
  width: 95vw;
  max-width: 1200px;
}

.background-wrapper img.bg {
  width: 100%;
}

.card-group {
  position: absolute;
  top: 40%;
  left: 5%;
  width: 90%;
  display: flex;
  justify-content: space-around;
  gap: 4%;
}

.card {
  width: 18%;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.card:hover {
  transform: scale(1.2);
}

.card img {
  width: 100%;
}

.card.hidden {
  display: none;
}

/* Custom popup message */
.popup {
  display: none;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: #4CAF50;
  color: white;
  padding: 30px 50px;
  border-radius: 12px;
  text-align: center;
  z-index: 1000;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
  animation: fadeIn 0.3s;
  font-family: 'Press Start 2P', cursive;
  font-size: 20px;
  font-weight: bold;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}









