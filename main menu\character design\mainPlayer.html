<!-- mainplayer.html -->
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Main Player</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Press+Start+2P&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="css/styles.css">
</head>
<body>

  <div class="background-wrapper">
    <img src="img/Frame 10.png" class="bg" alt="Background">

    <!-- Dynamic card image -->
    <div class="card">
      <img id="playerCharacter" src="img/Frame 11.png" alt="Player Card">
    </div>

    <div class="button-container">
      <!-- Customize Button -->
      <a href="nonPlayer.html" class="button-link">
        <div class="button" style="left: 68%;">
          <img src="img/Frame8.png" alt="Customize Button">
        </div>
      </a>
      
      <!-- Save Button -->
      <div class="button" style="left: 40%;" onclick="saveCharacter()">
        <img src="img/Group 80.png" alt="Save Button">
      </div>
    </div>
  </div>

  <script src="js/script.js"></script>
</body>
</html>
