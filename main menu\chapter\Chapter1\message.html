<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="message.css">
    <link href="https://fonts.googleapis.com/css2?family=Press+Start+2P&display=swap" rel="stylesheet">
    <title>Game Character with Numbered Background</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            font-family: 'Press Start 2P', cursive, sans-serif;
        }
        
        #game-container {
            position: relative;
            width: 100vw;
            height: 100vh;
            overflow: hidden;
        }
        
        #background {
            position: absolute;
            width: 100%;
            height: 100%;
            object-fit: cover;
            z-index: 0;
        }
        
        #ground-overlay {
            display: none; /* Hide instead of removing to preserve structure */
        }
        
        /* Character Container - Made larger */
        #character-container {
            position: absolute;
            bottom: -100px; /* Lower position (was 100px) */
            left: -100px;
            width: 600px; /* Increased from 500px */
            height: 650px; /* Increased from 550px */
            transition: left 4s cubic-bezier(0.25, 0.1, 0.25, 1);
            z-index: 2;
        }
        
        /* Character Image */
        #character {
            position: relative;
            width: 100%;
            height: 100%;
            background-image: url('npcs\ chatacter\ design\ 1.png');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center bottom;
            animation: walkBounce 0.8s infinite;
            transform-origin: bottom center;
            left: 50%;
        }
        
        /* Pointing Arm - Adjusted for larger character */
        #pointing-arm {
            position: absolute;
            width: 50px; /* Increased from 40px */
            height: 100px; /* Increased from 80px */
            /* background-color: #5D4037; */
            border-radius: 25px;
            right: 30px; /* Adjusted position */
            top: 100px; /* Adjusted position */
            transform-origin: top center;
            display: none;
            z-index: -1;
        }
        
        /* Improved Message Box - Adjusted position */
        #message-box {
            position: absolute;
            bottom: 400px; /* Decreased from 450px to move lower */
            left: 35%; /* Decreased from 42% to move more to the left */
            transform: translateX(-50%) scale(0);
            width: 350px;
            padding: 20px;
            background-color: rgba(222, 184, 135, 0.9);
            border-radius: 15px;
            border: 3px solid #8B4513;
            box-shadow: 0 5px 15px rgba(50, 12, 12, 0.5);
            opacity: 0;
            transition: all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            text-align: center;
            font-size: 18px;
            line-height: 1.6;
            color: #333;
            z-index: 3;
            font-family: 'Press Start 2P', cursive, sans-serif;
        }
        
        #message-box::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 100%;
            margin-left: -50px;
            border-width: 20px 20px 0;
            border-style: solid;
            border-color: burlywood  transparent;
        }
        
        /* Continue Button - Improved styling */
        #continue-btn {
            display: none; /* This will hide it if it still exists in the DOM */
        }
        
        /* Add fade-in animation for the Next button */
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        


    </style>
</head>
<body>
    <div id="game-container">
        <!-- Background image -->
        <img id="background" src="background.png" alt="Numbered background">
        
        <!-- Ground overlay for better visibility -->
        <div id="ground-overlay"></div>
        
        <!-- Character with Image -->
        <div id="character-container">
            <div id="character">
                <div id="pointing-arm"></div>
            </div>
        </div>
        
        <div id="message-box">
        </div>
        
        <!-- Removed the continue button -->
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const container = document.getElementById('character-container');
            const character = document.getElementById('character');
            const pointingArm = document.getElementById('pointing-arm');
            const messageBox = document.getElementById('message-box');
            const continueBtn = document.getElementById('continue-btn');
            
            // Start character walking
            setTimeout(() => {
                container.style.left = 'calc(50% - 100px)'; /* Adjusted for larger size */
                
                // After walking completes
                setTimeout(() => {
                    // Stop walking animation
                    character.style.animation = 'none';
                    
                    // Show pointing arm
                    pointingArm.style.display = 'block';
                    pointingArm.style.animation = 'pointArm 1s forwards';
                    
                    // Show message box with pop effect
                    setTimeout(() => {
                        messageBox.style.opacity = '1';
                        messageBox.style.transform = 'translateX(-50%) scale(1)';
                        
                        // Show continue button
                        setTimeout(() => {
                            continueBtn.style.display = 'block';
                        }, 1000);
                    }, 1000);
                }, 4000);
            }, 500);
            
            // Continue button action
            continueBtn.addEventListener('click', function() {
                alert('The adventure continues! (Next game scene would load here)');
            });
            
            // Adjust background image to cover the screen properly
            function resizeBackground() {
                const bg = document.getElementById('background');
                if (bg.complete) {
                    bg.style.width = '100%';
                    bg.style.height = '100%';
                    bg.style.objectFit = 'cover';
                }
            }
            
            // Initial resize and on window resize
            window.addEventListener('load', resizeBackground);
            window.addEventListener('resize', resizeBackground);
            document.getElementById('background').addEventListener('load', resizeBackground);
        });
    </script>
    <script>
    document.addEventListener('DOMContentLoaded', function () {
        const container = document.getElementById('character-container');
        const character = document.getElementById('character');
        const pointingArm = document.getElementById('pointing-arm');
        const messageBox = document.getElementById('message-box');
        // Removed reference to continue button since it no longer exists

        // New: message array
        const messages = [
            "Ah, traveler! Thank the spirits you've arrived. Our village is in dire trouble. The Sinmo has unleashed chaos upon us, destroying homes and leaving us in despair!",
            "We need your help to save our home. To restore what has been lost, we must rebuild the Chorten—the heart of our village. But for that, we need five treasures, each guarded by perilous quests.",
            "Each treasure holds a piece of the power we need. Complete these quests, and we can gather the treasures to restore the Chorten.",
            "Will you help us?"
        ];

        let messageIndex = 0;

        // Create a next button dynamically with improved styling
        const nextBtn = document.createElement('button');
        nextBtn.innerText = 'Next';
        nextBtn.style.position = 'absolute';
        nextBtn.style.bottom = '350px';
        nextBtn.style.left = '35%';
        nextBtn.style.transform = 'translateX(-50%)';
        nextBtn.style.padding = '10px 20px';
        nextBtn.style.background = '#8B4513';
        nextBtn.style.color = 'white';
        nextBtn.style.border = 'none';
        nextBtn.style.borderRadius = '20px';
        nextBtn.style.cursor = 'pointer';
        nextBtn.style.opacity = '0';
        nextBtn.style.display = 'none';
        nextBtn.style.zIndex = '4';
        nextBtn.style.fontFamily = "'Press Start 2P', cursive, sans-serif";
        nextBtn.style.fontSize = '14px';
        nextBtn.style.boxShadow = '0 4px 8px rgba(0,0,0,0.3)';
        nextBtn.style.transition = 'opacity 0.5s ease';
        document.getElementById('game-container').appendChild(nextBtn);

        // Removed back button code

        // Animate character popping up from center
        container.style.left = '50%';
        container.style.transform = 'translateX(-50%) translateY(100%) scale(0.5)';
        container.style.opacity = '0';
        container.style.transition = 'all 1s ease-out';

        setTimeout(() => {
            container.style.transform = 'translateX(-50%) translateY(0) scale(1)';
            container.style.opacity = '1';

            setTimeout(() => {
                pointingArm.style.display = 'block';
                pointingArm.style.animation = 'pointArm 1s forwards';

                setTimeout(() => {
                    messageBox.innerHTML = messages[messageIndex];
                    messageBox.style.opacity = '1';
                    messageBox.style.transform = 'translateX(-50%) scale(1)';
                    
                    // Show next button with fade-in effect AFTER message appears
                    setTimeout(() => {
                        nextBtn.style.display = 'block';
                        // Small delay to ensure display:block takes effect before opacity transition
                        setTimeout(() => {
                            nextBtn.style.opacity = '1';
                        }, 50);
                    }, 500);
                }, 1000);
            }, 500);
        }, 500);

        // On next message click
        nextBtn.addEventListener('click', () => {
            messageIndex++;
            if (messageIndex < messages.length) {
                messageBox.innerHTML = messages[messageIndex];
                
                // If this is the last message, hide next button and show Yes/No
                if (messageIndex === messages.length - 1) {
                    nextBtn.style.display = 'none';
                    
                    // Create Yes/No buttons directly below the message
                    const choiceContainer = document.createElement('div');
                    choiceContainer.className = 'choice-container';
                    choiceContainer.style.position = 'absolute';
                    choiceContainer.style.bottom = '300px';
                    choiceContainer.style.left = '35%';
                    choiceContainer.style.transform = 'translateX(-50%)';
                    choiceContainer.style.display = 'flex';
                    choiceContainer.style.gap = '20px';
                    choiceContainer.style.zIndex = '3';
                    document.getElementById('game-container').appendChild(choiceContainer);
                    
                    // Yes button
                    const yesButton = document.createElement('button');
                    yesButton.textContent = "Yes";
                    yesButton.style.padding = '12px 30px';
                    yesButton.style.background = 'linear-gradient(to bottom, #4CAF50 0%, #388E3C 100%)';
                    yesButton.style.color = 'white';
                    yesButton.style.border = 'none';
                    yesButton.style.borderRadius = '30px';
                    yesButton.style.cursor = 'pointer';
                    yesButton.style.fontSize = '16px';
                    yesButton.style.fontFamily = "'Press Start 2P', cursive, sans-serif";
                    yesButton.style.boxShadow = '0 4px 8px rgba(0,0,0,0.3)';
                    yesButton.style.transition = 'all 0.3s ease';
                    
                    // No button
                    const noButton = document.createElement('button');
                    noButton.textContent = "No";
                    noButton.style.padding = '12px 30px';
                    noButton.style.background = 'linear-gradient(to bottom, #f44336 0%, #d32f2f 100%)';
                    noButton.style.color = 'white';
                    noButton.style.border = 'none';
                    noButton.style.borderRadius = '30px';
                    noButton.style.cursor = 'pointer';
                    noButton.style.fontSize = '16px';
                    noButton.style.fontFamily = "'Press Start 2P', cursive, sans-serif";
                    noButton.style.boxShadow = '0 4px 8px rgba(0,0,0,0.3)';
                    noButton.style.transition = 'all 0.3s ease';
                    
                    // Add hover effects
                    yesButton.addEventListener('mouseover', () => {
                        yesButton.style.transform = 'translateY(-2px)';
                        yesButton.style.boxShadow = '0 6px 12px rgba(0,0,0,0.3)';
                    });
                    
                    yesButton.addEventListener('mouseout', () => {
                        yesButton.style.transform = 'translateY(0)';
                        yesButton.style.boxShadow = '0 4px 8px rgba(0,0,0,0.3)';
                    });
                    
                    noButton.addEventListener('mouseover', () => {
                        noButton.style.transform = 'translateY(-2px)';
                        noButton.style.boxShadow = '0 6px 12px rgba(0,0,0,0.3)';
                    });
                    
                    noButton.addEventListener('mouseout', () => {
                        noButton.style.transform = 'translateY(0)';
                        noButton.style.boxShadow = '0 4px 8px rgba(0,0,0,0.3)';
                    });
                    
                    // Add click events
                    yesButton.addEventListener('click', () => {
                        // Hide the message and choice container completely
                        messageBox.style.display = 'none';
                        choiceContainer.style.display = 'none';

                        // Hide all game elements first
                        const characterContainer = document.getElementById('character-container');
                        const background = document.getElementById('background');

                        if (characterContainer) characterContainer.style.display = 'none';

                        // Change background to bg pic-01.png
                        if (background) {
                            background.src = 'bg pic-01.png';
                        }

                        // Wait for background to change, then show loading screen
                        setTimeout(() => {
                            // Create loading overlay (transparent background since bg is already changed)
                            const loadingOverlay = document.createElement('div');
                            loadingOverlay.style.position = 'fixed';
                            loadingOverlay.style.top = '0';
                            loadingOverlay.style.left = '0';
                            loadingOverlay.style.width = '100%';
                            loadingOverlay.style.height = '100%';
                            loadingOverlay.style.backgroundColor = 'rgba(0, 0, 0, 0.3)'; // Semi-transparent overlay
                            loadingOverlay.style.display = 'flex';
                            loadingOverlay.style.flexDirection = 'column';
                            loadingOverlay.style.justifyContent = 'center';
                            loadingOverlay.style.alignItems = 'center';
                            loadingOverlay.style.zIndex = '9999';
                            loadingOverlay.style.color = 'white';
                            loadingOverlay.style.fontFamily = "'Press Start 2P', cursive, sans-serif";

                            // Create loading content
                            loadingOverlay.innerHTML = `
                                <div style="text-align: center; background: rgba(0, 0, 0, 0.7); padding: 40px; border-radius: 10px; border: 3px solid #fff;">
                                    <div style="font-size: 24px; margin-bottom: 30px; text-shadow: 2px 2px 4px rgba(0,0,0,0.8);">
                                        🎮 Loading FlappyDaka Game 🎮
                                    </div>
                                    <div class="loading-spinner" style="
                                        width: 60px;
                                        height: 60px;
                                        border: 6px solid rgba(255,255,255,0.3);
                                        border-top: 6px solid white;
                                        border-radius: 50%;
                                        animation: spin 1s linear infinite;
                                        margin: 0 auto 20px auto;
                                    "></div>
                                    <div style="font-size: 14px; opacity: 0.8;">
                                        Preparing your adventure...
                                    </div>
                                </div>
                            `;

                            // Add spinner animation
                            const style = document.createElement('style');
                            style.textContent = `
                                @keyframes spin {
                                    0% { transform: rotate(0deg); }
                                    100% { transform: rotate(360deg); }
                                }
                            `;
                            document.head.appendChild(style);

                            // Add loading overlay to page
                            document.body.appendChild(loadingOverlay);

                            // Redirect after 3 seconds
                            setTimeout(() => {
                                window.location.href = 'FlappyDaka Game-main/index.html';
                            }, 3000);
                        }, 300); // Short delay to let background change
                    });
                    
                    noButton.addEventListener('click', () => {
                        // Show goodbye message
                        messageBox.innerHTML = "I understand. Farewell, traveler...";
                        choiceContainer.style.display = 'none';
                        
                        // Redirect back to chapter selection after delay
                        setTimeout(() => {
                            window.location.href = "../chapter.html";
                        }, 2000);
                    });
                    
                    // Add buttons to container
                    choiceContainer.appendChild(yesButton);
                    choiceContainer.appendChild(noButton);
                }
            }
        });

        // Removed back button event listener

        // Adjust background image to fit
        function resizeBackground() {
            const bg = document.getElementById('background');
            if (bg.complete) {
                bg.style.width = '100%';
                bg.style.height = '100%';
                bg.style.objectFit = 'cover';
            }
        }

        window.addEventListener('load', resizeBackground);
        window.addEventListener('resize', resizeBackground);
        document.getElementById('background').addEventListener('load', resizeBackground);
    });
    
</script>

</body>
</html>
