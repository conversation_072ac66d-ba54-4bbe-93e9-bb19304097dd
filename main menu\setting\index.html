<!DOCTYPE html>
<html lang="en">
<head>
  <!-- (All your <head> content stays the same) -->
  <!-- ... -->
    <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Settings Page</title>
  <!-- Pixel-style font from Google Fonts -->
  <link
    href="https://fonts.googleapis.com/css2?family=Press+Start+2P&display=swap"
    rel="stylesheet"
  />
  <link rel="stylesheet" href="styles.css" />
</head>
<body>
  <div class="background">
    <div class="polygon"></div>

    <div class="panel">
      <!-- Panel Header (non-scrollable) -->
      <div class="panel-header">
        <h1>SETTINGS</h1>
        <!-- Tabs -->
        <div class="tabs">
          <div class="tab active" data-tab="audio">Audio</div>
          <div class="tab" data-tab="controls">Controls</div>
          <div class="tab" data-tab="gameplay">Gameplay</div>
        </div>
      </div>

      <!-- Scrollable Content Area -->
      <div class="panel-content">
        <div class="content">
        <!-- AUDIO SETTINGS -->
        <div class="tab-content" id="tab-audio">
          <div class="sliders-left">
            <div class="slider-group">
              <div class="label-container">
                <label for="masterVol">Master Volume</label>
              </div>
              <input type="range" id="masterVol" min="0" max="100" value="100" class="slider green-slider" />
              <span class="value" id="val-master">100</span>
            </div>
            <div class="slider-group">
              <div class="label-container">
                <label for="musicVol">Music Volume</label>
              </div>
              <input type="range" id="musicVol" min="0" max="100" value="100" class="slider green-slider" />
              <span class="value" id="val-music">100</span>
            </div>
            <div class="slider-group">
              <div class="label-container">
                <label for="sfxVol">SFX Volume</label>
              </div>
              <input type="range" id="sfxVol" min="0" max="100" value="100" class="slider green-slider" />
              <span class="value" id="val-sfx">100</span>
            </div>
          </div>
          <div class="sliders-right">
            <div class="slider-group">
              <div class="label-container">
                <label for="menuVol">Menu Sounds</label>
              </div>
              <input type="range" id="menuVol" min="0" max="100" value="100" class="slider red-slider" />
              <span class="value" id="val-menu">100</span>
            </div>
          </div>
        </div>

        <!-- CONTROLS SETTINGS -->
        <div class="tab-content" id="tab-controls" style="display:none;">
          <div class="controls-layout">
            <div class="controls-left">
              <div class="control-item">
                <div class="control-label">Pause</div>
                <div class="control-switch red-switch" data-control="pause">
                  <div class="switch-button"></div>
                </div>
              </div>
              <div class="control-item">
                <div class="control-label">Cursor Movement</div>
                <div class="control-button red-button" data-control="cursor">L</div>
              </div>
              <div class="control-item">
                <div class="control-label">Toggle Auto-Play</div>
                <div class="control-switch red-switch" data-control="autoplay1">
                  <div class="switch-button"></div>
                </div>
              </div>
              <div class="control-item">
                <div class="control-label">Toggle Auto-Play</div>
                <div class="control-switch red-switch" data-control="autoplay2">
                  <div class="switch-button"></div>
                </div>
              </div>
            </div>

            <div class="controller-diagram">
              <img src="Group 92.png" alt="Controller Diagram" />
            </div>

            <div class="controls-right">
              <div class="action-button">
                <div class="action-button-label green-button" data-action="confirm">A</div>
                <span class="action-text">Confirm</span>
              </div>
              <div class="action-button">
                <div class="action-button-label green-button" data-action="cancel">B</div>
                <span class="action-text">Cancel</span>
              </div>
              <div class="action-button">
                <div class="action-button-label green-button" data-action="setting">X</div>
                <span class="action-text">Setting</span>
              </div>
              <div class="action-button">
                <div class="action-button-label green-button" data-action="restart">Y</div>
                <span class="action-text">Restart</span>
              </div>
            </div>
          </div>
        </div>

        <!-- GAMEPLAY SETTINGS -->
        <div class="tab-content" id="tab-gameplay" style="display:none;">
          <div class="sliders-left">
            <div class="slider-group">
              <div class="label-container">
                <label for="textLang">Text Language</label>
              </div>
              <div class="dropdown-container">
                <select id="textLang" class="dropdown green-dropdown">
                  <option value="english">English</option>
                  <option value="spanish">Spanish</option>
                  <option value="french">French</option>
                  <option value="german">German</option>
                </select>
              </div>
            </div>
            <div class="slider-group">
              <div class="label-container">
                <label for="cameraDistance">Increased Camera Distance</label>
              </div>
              <div class="toggle-container">
                <button id="cameraDistance" class="toggle-button green-toggle active" data-value="off">
                  <span class="toggle-text">Off</span>
                  <span class="toggle-indicator"></span>
                </button>
              </div>
            </div>
            <div class="slider-group">
              <div class="label-container">
                <label for="infoOverlays">Info Overlays</label>
              </div>
              <div class="toggle-container">
                <button id="infoOverlays" class="toggle-button green-toggle active" data-value="on">
                  <span class="toggle-text">On</span>
                  <span class="toggle-indicator"></span>
                </button>
              </div>
            </div>
            <div class="slider-group">
              <div class="label-container">
                <label for="autoBlueprint">Auto Blueprint</label>
              </div>
              <div class="toggle-container">
                <button id="autoBlueprint" class="toggle-button green-toggle active" data-value="on">
                  <span class="toggle-text">On</span>
                  <span class="toggle-indicator"></span>
                </button>
              </div>
            </div>
            <div class="slider-group">
              <div class="label-container">
                <label for="tutorial">Tutorial</label>
              </div>
              <div class="toggle-container">
                <button id="tutorial" class="toggle-button green-toggle active" data-value="on">
                  <span class="toggle-text">On</span>
                  <span class="toggle-indicator"></span>
                </button>
              </div>
            </div>
            <div class="slider-group">
              <div class="label-container">
                <label for="productionChain">Production Chain Highlights</label>
              </div>
              <div class="toggle-container">
                <button id="productionChain" class="toggle-button green-toggle active" data-value="on">
                  <span class="toggle-text">On</span>
                  <span class="toggle-indicator"></span>
                </button>
              </div>
            </div>
            <div class="slider-group">
              <div class="label-container">
                <label for="skipMovies">Skip Movies</label>
              </div>
              <div class="toggle-container">
                <button id="skipMovies" class="toggle-button green-toggle" data-value="off">
                  <span class="toggle-text">Off</span>
                  <span class="toggle-indicator"></span>
                </button>
              </div>
            </div>
          </div>
          <div class="sliders-right">
            <div class="slider-group">
              <div class="label-container">
                <label for="textSpeed">Text Speed</label>
              </div>
              <div class="dropdown-container">
                <select id="textSpeed" class="dropdown red-dropdown">
                  <option value="slow">Slow</option>
                  <option value="normal">Normal</option>
                  <option value="fast" selected>Fast</option>
                </select>
              </div>
            </div>
            <div class="slider-group">
              <div class="label-container">
                <label for="verticalCamera">Vertical Camera Controls</label>
              </div>
              <div class="dropdown-container">
                <select id="verticalCamera" class="dropdown red-dropdown">
                  <option value="normal">Normal</option>
                  <option value="inverted">Inverted</option>
                  <option value="regular" selected>Regular</option>
                </select>
              </div>
            </div>
            <div class="slider-group">
              <div class="label-container">
                <label for="horizontalCamera">Horizontal Camera Controls</label>
              </div>
              <div class="dropdown-container">
                <select id="horizontalCamera" class="dropdown red-dropdown">
                  <option value="normal">Normal</option>
                  <option value="inverted">Inverted</option>
                  <option value="regular" selected>Regular</option>
                </select>
              </div>
            </div>
            <div class="slider-group">
              <div class="label-container">
                <label for="autosave">Autosave</label>
              </div>
              <div class="toggle-container">
                <button id="autosave" class="toggle-button red-toggle" data-value="off">
                  <span class="toggle-text">Off</span>
                  <span class="toggle-indicator"></span>
                </button>
              </div>
            </div>
          </div>
        </div>
        </div>
      </div>

      <!-- Save Button (positioned outside scrollable content) -->
      <button class="save-image-button">
        <img src="Group 80.png" alt="Save" />
      </button>
    </div>
  </div>

  <script src="script.js"></script>
</body>
</html>
