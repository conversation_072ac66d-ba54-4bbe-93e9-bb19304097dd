// DOM elements
const girl = document.getElementById('girl');
const man = document.getElementById('man');
const scrollPopup = document.getElementById('scroll-popup');
const closeButton = document.getElementById('close-scroll');
const nextButton = document.getElementById('nextButton');

// Next button functionality (same as close button)
nextButton.addEventListener('click', () => {
    // Redirect to message.html when next is clicked
    window.location.href = "message.html";
});

// Positions and sizes
const manTop = 250; // Updated to match CSS
const manHeight = 150; // Updated to match actual height
const girlHeight = 140; // Updated to match actual height
const speed = 1; // Pixels per frame

// Start animation automatically
function startAnimation() {
    let girlTop = 600; // Starting position further down
    let girlLeft = 120; // Starting more to the left
    
    // Set initial position and make visible
    girl.style.top = girlTop + 'px';
    girl.style.left = girlLeft + 'px';
    girl.style.visibility = 'visible';
    
    function animate() {
        // Move up
        girlTop -= speed;
        girl.style.top = girlTop + 'px';
        
        // Move right gradually
        const targetLeft = 160;
        const totalDistance = 600 - (manTop + manHeight - 30); // Total vertical distance
        const currentDistance = 600 - girlTop; // Current distance traveled
        const progress = currentDistance / totalDistance; // Progress ratio
        
        // Calculate new left position based on progress
        const newLeft = 120 + (targetLeft - 120) * progress;
        girl.style.left = newLeft + 'px';
        
        // Calculate positions - we want the girl to stop below the man
        const stopPosition = manTop + manHeight - 30; // 30px overlap with man (increased from 10px)
        
        // Continue animation if girl hasn't reached the stop position yet
        if (girlTop > stopPosition) {
            requestAnimationFrame(animate);
        } else {
            // Snap to perfect position when done
            girl.style.top = stopPosition + 'px';
            girl.style.left = targetLeft + 'px';
            
            // Show the scroll popup after the girl stops
            setTimeout(() => {
                scrollPopup.style.display = 'block';
                scrollPopup.classList.add('scroll-animate');
            }, 500); // Delay of 500ms after stopping
        }
    }
    
    // Small delay before starting animation
    setTimeout(animate, 200);
}

// Close popup functionality
closeButton.addEventListener('click', () => {
    scrollPopup.style.display = 'none';
    // Redirect to message.html after closing the scroll
    window.location.href = "message.html";
});

// Start the animation when the page loads
document.addEventListener('DOMContentLoaded', startAnimation);

// Initialize left-side bars
function createSegmentedBar(containerId, segmentCount, activeCount, className) {
  const container = document.getElementById(containerId);
  container.innerHTML = '';
  
  for (let i = 0; i < segmentCount; i++) {
    const segment = document.createElement('div');
    segment.className = `bar-segment ${i < activeCount ? 'active' : ''}`;
    segment.style.setProperty('--segment-index', i);
    container.appendChild(segment);
  }
}

// Initialize the left-side bars when the page loads
window.addEventListener('DOMContentLoaded', () => {
  // Initialize bars (9/10 health, 7/10 mana)
  createSegmentedBar('left-health-bar', 10, 9, 'health');
  createSegmentedBar('left-mana-bar', 10, 7, 'mana');
});

// Remove the automatic redirect after 10 seconds
// The setTimeout function in the HTML should be removed or commented out
