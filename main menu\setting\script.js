// Link slider values
function linkSliderToValue(sliderId, valueId) {
  const slider = document.getElementById(sliderId);
  const valueSpan = document.getElementById(valueId);
  slider.addEventListener("input", () => {
    valueSpan.textContent = slider.value;
  });
}

linkSliderToValue("masterVol", "val-master");
linkSliderToValue("musicVol", "val-music");
linkSliderToValue("sfxVol", "val-sfx");
linkSliderToValue("menuVol", "val-menu");

// Tab switching
const tabs = document.querySelectorAll(".tab");
const contents = {
  audio: document.getElementById("tab-audio"),
  controls: document.getElementById("tab-controls"),
  gameplay: document.getElementById("tab-gameplay")
};

tabs.forEach(tab => {
  tab.addEventListener("click", () => {
    // Remove active class from all tabs
    tabs.forEach(t => t.classList.remove("active"));
    // Add active to clicked tab
    tab.classList.add("active");

    // Hide all contents
    Object.values(contents).forEach(c => c.style.display = "none");

    // Show selected content
    const selectedTab = tab.dataset.tab;
    contents[selectedTab].style.display = "flex";
  });
});

// Toggle button functionality for gameplay settings
document.addEventListener('DOMContentLoaded', function() {
  const toggleButtons = document.querySelectorAll('.toggle-button');

  toggleButtons.forEach(button => {
    button.addEventListener('click', function() {
      const currentValue = this.dataset.value;
      const textSpan = this.querySelector('.toggle-text');

      if (currentValue === 'on') {
        this.dataset.value = 'off';
        textSpan.textContent = 'Off';
        this.classList.remove('active');
      } else {
        this.dataset.value = 'on';
        textSpan.textContent = 'On';
        this.classList.add('active');
      }
    });
  });

  // Control switches functionality
  const controlSwitches = document.querySelectorAll('.control-switch');

  controlSwitches.forEach(switchElement => {
    switchElement.addEventListener('click', function() {
      const control = this.dataset.control;
      const isActive = this.classList.contains('active');

      if (isActive) {
        this.classList.remove('active');
        console.log(`${control} disabled`);
      } else {
        this.classList.add('active');
        console.log(`${control} enabled`);
      }
    });
  });

  // Control buttons functionality
  const controlButtons = document.querySelectorAll('.control-button');

  controlButtons.forEach(button => {
    button.addEventListener('click', function() {
      const control = this.dataset.control;
      console.log(`${control} button pressed`);

      // Add visual feedback
      this.style.transform = 'translateY(2px)';
      setTimeout(() => {
        this.style.transform = 'translateY(-2px)';
      }, 150);
    });
  });

  // Action buttons functionality
  const actionButtons = document.querySelectorAll('.action-button-label');

  actionButtons.forEach(button => {
    button.addEventListener('click', function(e) {
      e.stopPropagation();
      const action = this.dataset.action;

      switch(action) {
        case 'confirm':
          console.log('Confirm action triggered');
          alert('Confirm button pressed!');
          break;
        case 'cancel':
          console.log('Cancel action triggered');
          alert('Cancel button pressed!');
          break;
        case 'setting':
          console.log('Setting action triggered');
          alert('Setting button pressed!');
          break;
        case 'restart':
          console.log('Restart action triggered');
          if (confirm('Are you sure you want to restart?')) {
            alert('Restart confirmed!');
          }
          break;
      }
    });
  });

  // Save button scroll visibility functionality
  const panelContent = document.querySelector('.panel-content');
  const saveButton = document.querySelector('.save-image-button');

  if (panelContent && saveButton) {
    panelContent.addEventListener('scroll', function() {
      // Calculate if user has scrolled to near the bottom
      const scrollTop = panelContent.scrollTop;
      const scrollHeight = panelContent.scrollHeight;
      const clientHeight = panelContent.clientHeight;

      // Show save button when user is within 50px of the bottom
      const isNearBottom = scrollTop + clientHeight >= scrollHeight - 50;

      if (isNearBottom) {
        saveButton.classList.add('show');
      } else {
        saveButton.classList.remove('show');
      }
    });

    // Also check on tab switch to handle different content heights
    const tabs = document.querySelectorAll('.tab');
    tabs.forEach(tab => {
      tab.addEventListener('click', function() {
        // Small delay to allow content to load
        setTimeout(() => {
          const scrollTop = panelContent.scrollTop;
          const scrollHeight = panelContent.scrollHeight;
          const clientHeight = panelContent.clientHeight;

          // If content doesn't need scrolling, show save button immediately
          if (scrollHeight <= clientHeight) {
            saveButton.classList.add('show');
          } else {
            // Check if already at bottom
            const isNearBottom = scrollTop + clientHeight >= scrollHeight - 50;
            if (isNearBottom) {
              saveButton.classList.add('show');
            } else {
              saveButton.classList.remove('show');
            }
          }
        }, 100);
      });
    });

    // Initial check when page loads
    setTimeout(() => {
      const scrollHeight = panelContent.scrollHeight;
      const clientHeight = panelContent.clientHeight;

      // If content doesn't need scrolling, show save button immediately
      if (scrollHeight <= clientHeight) {
        saveButton.classList.add('show');
      }
    }, 100);
  }
});
