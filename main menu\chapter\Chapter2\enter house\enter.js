const player = document.getElementById("player");

// Messages array (removed duplicate)
const messages = [
  "Listen closely, traveler.",
  "The Sinmo was once a compassionate woman, beloved by our village.",
  "She tended to the sick and nurtured the land. But jealousy and misunderstanding turned her heart to darkness.",
  "The people wronged her, casting her aside when she needed them most. In her pain, she became the very thing we fear.",
  "To subdue the Sinmo and restore peace, you must collect the relic from the chorten. It holds the power to heal her spirit.",
  "Will you help us bring her back?"
];

let messageIndex = 0;
let isTyping = false; // Prevent button spam during typing
let typingInterval = null; // Store the typing interval
let currentMessage = ""; // Store the current message being typed
const textElement = document.getElementById("dialogue-text");
const nextBtn = document.getElementById("next-btn");
const continueBtn = document.getElementById("continue-btn");
const choiceContainer = document.getElementById("choice-container");
const yesBtn = document.getElementById("yes-btn");
const noBtn = document.getElementById("no-btn");

// Target Y position (where player should stop near housewife)
const stopAt = 350; // Increased distance for more space

// Move player upwards (simulate walking)
function moveUp() {
  let topPosition = parseInt(window.getComputedStyle(player).top);

  const interval = setInterval(() => {
    if (topPosition <= stopAt) {
      clearInterval(interval);
      console.log("Player reached the housewife.");
      // Start dialogue after player stops
      setTimeout(() => {
        startDialogue();
      }, 500); // Small delay after stopping
      return;
    }

    topPosition -= 2; // speed
    player.style.top = topPosition + "px";
  }, 20); // frame rate
}

// Typing effect with better control
function typeMessage(message, callback) {
  if (isTyping) return; // Prevent overlapping typing

  isTyping = true;
  currentMessage = message; // Store the current message
  textElement.innerHTML = "";
  let i = 0;

  typingInterval = setInterval(() => {
    textElement.innerHTML += message.charAt(i);
    i++;
    if (i >= message.length) {
      clearInterval(typingInterval);
      typingInterval = null;
      isTyping = false;
      if (callback) callback();
    }
  }, 50); // Slightly slower for better readability
}

// Function to complete typing instantly
function completeTyping() {
  if (isTyping && typingInterval) {
    clearInterval(typingInterval);
    typingInterval = null;
    textElement.innerHTML = currentMessage;
    isTyping = false;
  }
}

// Start dialogue system
function startDialogue() {
  // Set up text styling
  textElement.style.fontFamily = "'Press Start 2P', cursive";
  textElement.style.fontSize = "12px";
  textElement.style.color = "black";
  textElement.style.textAlign = "center";

  // Display first message
  typeMessage(messages[messageIndex]);
}

// Next button handler with improved UX
nextBtn.addEventListener("click", () => {
  // If currently typing, complete the typing instantly
  if (isTyping) {
    completeTyping();
    return;
  }

  // If not typing, advance to next message
  messageIndex++;
  if (messageIndex < messages.length) {
    typeMessage(messages[messageIndex]);
  }

  // Show Yes/No buttons on last message (when asking "Will you help us bring her back?")
  if (messageIndex === messages.length - 1) {
    nextBtn.style.display = "none";
    choiceContainer.style.display = "flex";
  }
});

// Yes button handler
yesBtn.addEventListener("click", () => {
  if (isTyping) return; // Don't allow clicking during typing

  // Show loading screen before redirecting to next scene
  const loadingOverlay = document.createElement('div');
  loadingOverlay.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    font-family: 'Press Start 2P', cursive;
    color: white;
    font-size: 24px;
  `;

  const loadingText = document.createElement('div');
  loadingText.textContent = 'Loading...';
  loadingOverlay.appendChild(loadingText);

  const spinner = document.createElement('div');
  spinner.style.cssText = `
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    animation: spin 2s linear infinite;
    margin-top: 20px;
  `;
  loadingOverlay.appendChild(spinner);

  // Add spinner animation
  const style = document.createElement('style');
  style.textContent = `
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  `;
  document.head.appendChild(style);

  document.body.appendChild(loadingOverlay);

  // Redirect after 3 seconds
  setTimeout(() => {
    window.location.href = "Memory Game/index.html";
  }, 3000);
});

// No button handler
noBtn.addEventListener("click", () => {
  if (isTyping) return; // Don't allow clicking during typing

  // Show goodbye message
  textElement.innerHTML = "I understand. Farewell, traveler...";
  choiceContainer.style.display = "none";

  // Redirect back to chapter selection after delay
  setTimeout(() => {
    window.location.href = "../../chapter.html";
  }, 2000);
});

// Initialize everything when page loads
window.onload = () => {
  moveUp();
};
