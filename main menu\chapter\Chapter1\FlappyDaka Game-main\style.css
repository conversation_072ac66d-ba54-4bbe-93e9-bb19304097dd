html, body {
    height: 100%;
    width: 100%;
}

* {
	margin: 0;
	padding: 0;
	box-sizing: border-box;
	font-family: 'Press Start 2P', cursive;
}
.background {
	height: 100vh;
	width: 100vw;
	background: url('images/bg\ pic-01.png') no-repeat center center fixed;
    -webkit-background-size: cover;
    -moz-background-size: cover;
    -o-background-size: cover;
    background-size: cover;
}
.bird {
	height: 80px;
	width: 80px;
	position: fixed;
	top: 40vh;
	left: 30vw;
	z-index: 100;
}
.pipe_sprite {
	position: fixed;
	top: 20vh; 
	left: 60vw;
	height: 70vh;
	width: 6vw;
	background:radial-gradient(lightgreen 50%, green);
	border: 5px solid black;
}
.message {
	position: absolute;
	z-index: 10;
	color: black;
	top: 30%;
	left: 50%;
	font-size: 2.5em; /* Increased from 2em to 2.5em */
	transform: translate(-50%, -50%);
	text-align: center;
}
.messageStyle{
	background: white;
	padding: 30px;
	box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
	border-radius: 5%;
}
.score {
	position: fixed;
	z-index: 10;
	height: 10vh;
	font-size: 8vh; /* Reduced from 10vh to 8vh */
	font-weight: 100;
	color: white;
	-webkit-text-stroke-width: 2px;
    -webkit-text-stroke-color: black;
	top: 0;
	left: 0;
	margin: 10px;
	font-family: 'Press Start 2P', cursive;
}
.score_val {
	color: gold;
	font-weight: bold;
}
@media only screen and (max-width: 1080px) {
    .message{
		font-size: 50px;
		top: 50%;
		white-space: nowrap;
	}
	.score{
		font-size: 8vh;
	}
	.bird{
		width: 60px;
		height: 40px;
	}
	.pipe_sprite{
		width: 4vw;
	}
}




