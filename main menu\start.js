// Wait for DOM to load
document.addEventListener('DOMContentLoaded', function() {
    // Switch to main menu when start is pressed
    document.getElementById('startButton').addEventListener('click', function() {
        document.getElementById('startScreen').style.display = 'none';
        document.getElementById('mainMenu').style.display = 'flex';
    });
    
    // Handle menu button clicks
    document.querySelectorAll('.menu-button').forEach(button => {
        button.addEventListener('click', function() {
            const action = this.textContent.trim();
            
            switch(action) {
                case 'START GAME':
                    window.location.href = 'game.html';
                    break;
                case 'PLAYER PROFILE':
                    // Add profile screen logic
                    break;
                case 'CHARACTER DESIGN':
                    // Add character design logic
                    break;
                case 'SETTINGS':
                    // Add settings logic
                    break;
            }
        });
    });
});
