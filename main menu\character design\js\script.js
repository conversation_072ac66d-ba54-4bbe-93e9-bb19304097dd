document.addEventListener('DOMContentLoaded', function() {
  const selectedCharacter = localStorage.getItem('selectedCharacter');
  if (selectedCharacter) {
    document.getElementById('playerCharacter').src = selectedCharacter;
  } else {
    // Ensure Frame 11 is the default when no character is selected
    document.getElementById('playerCharacter').src = 'img/Frame 11.png';
  }
});

function saveCharacter() {
  const currentCharacter = document.getElementById('playerCharacter').src;
  localStorage.setItem('savedCharacter', currentCharacter);

  // Clear the selected character so user can select again
  localStorage.removeItem('selectedCharacter');

  // Create and show popup
  const popup = document.createElement('div');
  popup.className = 'popup';
  popup.textContent = 'Character saved successfully!';
  document.body.appendChild(popup);

  // Show popup
  popup.style.display = 'block';

  // Hide popup and redirect after delay
  setTimeout(function() {
    popup.style.display = 'none';
    window.location.href = '../start.html'; // Return to main menu
  }, 1500);
}

// Fix redirect after saving character
setTimeout(function() {
  popup.style.display = 'none';
  window.location.href = '../start.html'; // Return to main menu
}, 1500);


