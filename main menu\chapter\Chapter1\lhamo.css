html, body {
    margin: 0;
    padding: 0;
    height: 100%;
    width: 100%;
    overflow: hidden;
    font-family: 'Press Start 2P', cursive, sans-serif;
    touch-action: manipulation;
}

body {
    background-image: url('damagevillage.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background-color: white;
    min-height: 100vh;
}

#scene {
    position: relative;
    width: 400px;
    height: 600px;
    margin: 20px auto;
}

#man {
    position: absolute;
    width: 70px;
    height: 150px;
    background-image: url('npcs\ chatacter\ design\ 1.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    left: 150px;
    top: 250px;
    z-index: 5; /* Reduced from 10 to be lower than the girl */
}

#girl {
    position: absolute;
    width: 50px;
    height: 140px;
    background-image: url('character\ back\ 1.png');
    background-size: contain; /* Add this to ensure image fits properly */
    background-repeat: no-repeat; /* Add this to prevent repeating */
    background-position: center; /* Add this to center the image */
    left: 120px; /* Starting more to the left */
    top: 600px; /* Starting position */
    visibility: hidden; /* Hide initially until JS sets position */
    z-index: 10; /* Higher than man's z-index to overlap/hide him */
}

/* Add animation for scroll popup */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translate(-50%, -40%) scale(0.8);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

#scroll-popup {
    position: fixed;
    height: 200px;
    top: 80%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 500px;
    background: url('Layer_1.png') center/cover;
    z-index: 100;
    display: none;
    opacity: 0;
}

.scroll-animate {
    animation: fadeIn 0.6s ease-out forwards;
}

#scroll-content {
    padding: 15px;
    border-radius: 3px;
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: flex-start; /* Keeping top alignment */
    padding-top: 50px; /* Increased from 40px to move text down */
    height: 100%;
}

.letter {
    margin: 0;
    padding: 0 20px;
    font-size: 16px; /* Keeping the larger font size */
    line-height: 1.4;
}

#scroll-content h1 {
    font-size: 20px;
    margin: 0 0 10px 0;
    color: #5C3A21;
    text-align: center;
    border-bottom: 1px solid #8B4513;
    padding-bottom: 5px;
}

#close-scroll {
    position: absolute;
    top: 10px;
    right: 10px;
    background: #8B4513;
    color: white;
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    font-weight: bold;
    font-size: 24px;
    box-shadow: 0 0 5px rgba(0,0,0,0.5);
    display: flex;
    justify-content: center;
    align-items: center;
}

/* Add animation for Next button fade in */
@keyframes buttonFadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

#skipButton {
    position: fixed;
    bottom: 60px;
    left: 50%;
    transform: translateX(-50%);
    background: #8B4513;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    font-weight: bold;
    font-size: 16px;
    z-index: 100;
    font-family: 'Press Start 2P', cursive;
    display: none;
    box-shadow: 0 0 8px rgba(0,0,0,0.5);
    opacity: 0;
    animation: buttonFadeIn 0.6s ease-out forwards;
    animation-delay: 0.2s;
    margin: 0 auto; /* Add this to help with centering */
}

/* Add this new style for the Next button in the scroll popup */
#scroll-content .next-button {
    display: block;
    margin: 15px auto 0;
    background: #8B4513;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    font-weight: bold;
    font-size: 16px;
    font-family: 'Press Start 2P', cursive;
    box-shadow: 0 0 8px rgba(0,0,0,0.5);
}

#scroll-content .next-button:hover {
    background: #6B2A00;
}

#skipButton:hover {
    background: #6B2A00;
}

/* Left-side health and mana bars - significantly larger and further from edge */
.left-stats {
  position: fixed;
  top: 60px;  /* Moved further down from edge */
  left: 60px;  /* Moved further right from edge */
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 300px;  /* Further increased width */
}

.bar-group {
  display: flex;
  flex-direction: column;
  gap: 20px;  /* Increased gap between bars */
}

.bar-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  position: relative;
  padding-left: 40px;  /* More padding for larger icon */
  height: 50px;  /* Increased height */
}

.bar-icon {
  position: absolute;
  font-size: 26px;  /* Much larger icon */
  z-index: 3;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  filter: drop-shadow(0 0 4px currentColor);  /* Enhanced shadow */
}

.bar-segments {
  display: flex;
  gap: 6px;  /* Larger gap between segments */
  padding: 8px;  /* Increased padding */
  background: rgba(255, 255, 255, 0.8);  /* Changed to white with slight transparency */
  border: 4px solid #444;  /* Thicker border */
  border-radius: 8px;  /* Increased border radius */
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);  /* Enhanced shadow */
  align-items: center;
}

.bar-segment {
  width: 18px;  /* Larger segment size */
  height: 18px;  /* Larger segment size */
  background: #222;
  border: 1px solid #000;
  position: relative;
  border-radius: 4px;  /* Increased border radius */
  overflow: hidden;
}

.health .bar-segment {
  background: linear-gradient(to bottom, #3a0000, #222);
}

.health .bar-segment.active {
  background: linear-gradient(to bottom, #ff0000, #aa0000);
  box-shadow: 0 0 6px rgba(255, 0, 0, 0.8);
}

.mana .bar-segment {
  background: linear-gradient(to bottom, #00203a, #222);
}

.mana .bar-segment.active {
  background: linear-gradient(to bottom, #00a8ff, #0062aa);
  box-shadow: 0 0 6px rgba(0, 168, 255, 0.8);
}

.health .bar-segments {
  box-shadow: inset 0 0 8px rgba(255, 0, 0, 0.3), 0 0 10px rgba(0, 0, 0, 0.5);
}

.mana .bar-segments {
  box-shadow: inset 0 0 8px rgba(0, 168, 255, 0.3), 0 0 10px rgba(0, 0, 0, 0.5);
}

.health .bar-icon {
  color: red;
}

.mana .bar-icon {
  color: dodgerblue;
}

@keyframes segment-fill {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.bar-segment.active {
  animation: segment-fill 0.3s ease-out forwards;
  animation-delay: calc(var(--segment-index) * 0.1s);
}
