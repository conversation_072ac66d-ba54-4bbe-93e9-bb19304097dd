document.addEventListener('DOMContentLoaded', () => {
    // DOM elements
    const loadingScreen = document.getElementById('loading-screen');
    const welcomeScreen = document.getElementById('welcome-screen');
    const gameScreen = document.getElementById('game-screen');
    const winScreen = document.getElementById('win-screen');
    const gameBoard = document.getElementById('game-board');
    const movesDisplay = document.getElementById('moves');
    const pairsDisplay = document.getElementById('pairs');
    const totalPairsDisplay = document.getElementById('total-pairs');
    const totalMovesDisplay = document.getElementById('total-moves');
    const finalDifficultyDisplay = document.getElementById('final-difficulty');
    const difficultyDisplay = document.getElementById('difficulty-display');

    // Audio elements
    const flipSound = document.getElementById('flip-sound');
    const matchSound = document.getElementById('match-sound');
    const winSound = document.getElementById('win-sound');
    const lofiMusic = document.getElementById('lofi-music');

    // Buttons
    const easyBtn = document.getElementById('easy-btn');
    const mediumBtn = document.getElementById('medium-btn');
    const hardBtn = document.getElementById('hard-btn');
    const backBtn = document.getElementById('back-btn');
    const playAgainBtn = document.getElementById('play-again-btn');
    const resetBtn = document.getElementById('reset-btn');
    const winBackBtn = document.getElementById('win-back-btn');
    const musicToggle = document.getElementById('music-toggle');

    // Game variables
    let cards = [];
    let flippedCards = [];
    let matchedPairs = 0;
    let moves = 0;
    let totalMoves = 0;
    let difficulty = '';
    let pairsToMatch = 0;
    let canFlip = true;
    let isMusicPlaying = false;

    // Images for cards
    const images = ['archery.png', 'blue.png', 'boy.webp', 'crown.svg.png', 'flag.png', 'girl.png', 'map.svg.png', 'Taktsang.png','bluee.png'];

    // Initialize game
    initGame();

    function initGame() {
        // Simulate loading assets
        setTimeout(() => {
            loadingScreen.style.display = 'none';
            welcomeScreen.classList.remove('hidden');

            // Preload sounds
            flipSound.load();
            matchSound.load();
            winSound.load();

            // Setup lofi music
            setupLofiMusic();
        }, 1500);

        // Event listeners
        easyBtn.addEventListener('click', () => startGame('easy'));
        mediumBtn.addEventListener('click', () => startGame('medium'));
        hardBtn.addEventListener('click', () => startGame('hard'));
        backBtn.addEventListener('click', returnToMenu);
        playAgainBtn.addEventListener('click', restartCurrentGame);
        resetBtn.addEventListener('click', resetCurrentGame);
        winBackBtn.addEventListener('click', returnToMenu);
        musicToggle.addEventListener('click', toggleMusic);
    }

    // Start game with selected difficulty
    function startGame(diff) {
        difficulty = diff;
        totalMoves = 0;

        // Set number of pairs based on difficulty
        switch (difficulty) {
            case 'easy':
                pairsToMatch = 4;
                break;
            case 'medium':
                pairsToMatch = 6;
                break;
            case 'hard':
                pairsToMatch = 8;
                break;
        }

        // Update displays
        difficultyDisplay.textContent = difficulty.charAt(0).toUpperCase() + difficulty.slice(1);
        finalDifficultyDisplay.textContent = difficulty.charAt(0).toUpperCase() + difficulty.slice(1);

        // Start music immediately when entering the game
        if (!isMusicPlaying) {
            playLofiMusic();
        }

        welcomeScreen.classList.add('hidden');
        showLoadingAnimation(() => {
            gameScreen.classList.remove('hidden');
            setupGame();
        });
    }

    // Show loading animation
    function showLoadingAnimation(callback) {
        loadingScreen.style.display = 'flex';
        setTimeout(() => {
            loadingScreen.style.display = 'none';
            if (callback) callback();
        }, 800);
    }

    // Set up the game
    function setupGame() {
        matchedPairs = 0;
        moves = 0;
        movesDisplay.textContent = moves;
        pairsDisplay.textContent = matchedPairs;
        totalPairsDisplay.textContent = pairsToMatch;

        // Clear the board
        gameBoard.innerHTML = '';
        cards = [];
        flippedCards = [];
        canFlip = true;

        // Create cards
        createCards();
    }

    // Create cards for the game
    function createCards() {
        // Shuffle the full image set and select the required number of unique pairs
        const shuffledImages = [...images];
        shuffleArray(shuffledImages);
        const gameImages = shuffledImages.slice(0, pairsToMatch);

        // Duplicate for pairs
        let cardImages = [...gameImages, ...gameImages];

        // Shuffle the card images
        shuffleArray(cardImages);

        // Create card elements
        cardImages.forEach((image, index) => {
            const card = document.createElement('div');
            card.className = 'card';
            card.dataset.index = index;
            card.dataset.image = image;

            card.innerHTML = `
            <div class="card-inner">
                <div class="card-front">?</div>
                <div class="card-back"><img src="img/${image}" alt="Card Image"></div>
            </div>
        `;

            card.addEventListener('click', flipCard);
            gameBoard.appendChild(card);
            cards.push(card);
        });

        // Adjust grid layout based on card count
        const cardCount = cardImages.length;
        let columns = 4;
        if (cardCount > 16) columns = 6;

        gameBoard.style.gridTemplateColumns = `repeat(${columns}, 1fr)`;
    }

    // Flip a card when clicked
    function flipCard() {
        if (!canFlip) return;
        if (this.classList.contains('flipped')) return;
        if (flippedCards.length >= 2) return;

        // Play flip sound
        flipSound.currentTime = 0;
        flipSound.play();

        this.classList.add('flipped');
        flippedCards.push(this);

        // If two cards are flipped, check for a match
        if (flippedCards.length === 2) {
            moves++;
            movesDisplay.textContent = moves;
            canFlip = false;

            const card1 = flippedCards[0];
            const card2 = flippedCards[1];

            if (card1.dataset.image === card2.dataset.image) {
                // Match found
                matchedPairs++;
                pairsDisplay.textContent = matchedPairs;

                // Play match sound
                matchSound.currentTime = 0;
                matchSound.play();

                // Add matched class and disable cards
                flippedCards.forEach(card => {
                    card.classList.add('matched');
                    card.removeEventListener('click', flipCard);
                });

                flippedCards = [];
                canFlip = true;

                // Check if game is complete
                if (matchedPairs === parseInt(totalPairsDisplay.textContent)) {
                    totalMoves += moves;

                    // Game completed
                    setTimeout(() => {
                        gameScreen.classList.add('hidden');
                        totalMovesDisplay.textContent = totalMoves;
                        winSound.currentTime = 0;
                        winSound.play();
                        winScreen.classList.remove('hidden');
                    }, 1000);
                }
            } else {
                // No match, flip cards back after delay
                setTimeout(() => {
                    flippedCards.forEach(card => {
                        card.classList.remove('flipped');
                    });
                    flippedCards = [];
                    canFlip = true;
                }, 1000);
            }
        }
    }

    // Reset current game (same difficulty)
    function resetCurrentGame() {
        showLoadingAnimation(() => {
            setupGame();
        });
    }

    // Restart current game (from win screen)
    function restartCurrentGame() {
        winScreen.classList.add('hidden');
        showLoadingAnimation(() => {
            gameScreen.classList.remove('hidden');
            setupGame();
        });
    }

    // Return to main menu
    function returnToMenu() {
        gameScreen.classList.add('hidden');
        winScreen.classList.add('hidden');
        setTimeout(() => {
            welcomeScreen.classList.remove('hidden');
        }, 500);
    }

    // Helper function to shuffle an array
    function shuffleArray(array) {
        for (let i = array.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [array[i], array[j]] = [array[j], array[i]];
        }
        return array;
    }

    // Setup lofi background music
    function setupLofiMusic() {
        lofiMusic.volume = 0.3; // Set volume to 30%
        lofiMusic.loop = true;
    }

    // Play lofi music
    function playLofiMusic() {
        lofiMusic.play().then(() => {
            isMusicPlaying = true;
            musicToggle.textContent = '🎵';
            musicToggle.classList.remove('muted');
            musicToggle.title = 'Mute Background Music';
        }).catch(error => {
            console.log('Could not play music:', error);
        });
    }

    // Pause lofi music
    function pauseLofiMusic() {
        lofiMusic.pause();
        isMusicPlaying = false;
        musicToggle.textContent = '🔇';
        musicToggle.classList.add('muted');
        musicToggle.title = 'Unmute Background Music';
    }

    // Toggle music on/off
    function toggleMusic() {
        if (isMusicPlaying) {
            pauseLofiMusic();
        } else {
            playLofiMusic();
        }
    }
});