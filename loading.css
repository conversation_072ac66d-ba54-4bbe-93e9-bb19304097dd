body {
	padding: 0;
	margin: 0;
	font-family: 'Press Start 2P', cursive;
	color: white;
	font-size: 12px; /* This font is pixel-style, so use smaller base size */
}

.tagline,
.loading-text {
	font-family: 'Press Start 2P', cursive;
	font-size: 10px;
	color: #ccc;
}

.progress-bar {
	font-family: 'Press Start 2P', cursive;
}
*, *::before, *::after {
	box-sizing: border-box;
}
.background {
  z-index: -2;  /* Lowest layer */
}
.stars-container {
  z-index: -1;  /* Above background but below content */
}
.logo, .girl-character, .menu {
  z-index: 1;   /* Topmost layer */
}
.stars-container {
  border: 2px solid red; /* Debug line - remove later */
}
body {
	padding: 0;
	margin: 0;
}

/* Full-screen black background only on large screens */
@media (min-width: 768px) {
	html, body {
		background-color: black;
		width: 100vw;
		height: 100vh;
		display: flex;
		justify-content: center;
		align-items: center;
	}
}
.container {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 1em;
	text-align: center;
}

.logo {
	max-width: 100%;
	width: 500px;
	object-fit: contain;
}
.tagline {
	font-family: 'Press Start 2P', cursive;
	font-size: 10px;
	color: #ccc;
	margin-top: 0;       /* Tight to logo */
	margin-bottom: 2em;  /* Space *below* tagline, before mascot */
}

@keyframes jump {
	0%, 100% {
		transform: translateY(0);
	}
	50% {
		transform: translateY(-20px);
	}
}

.mascot {
	width: 60px;
	height: auto;
	animation: jump 1s infinite ease-in-out;
}

.loading-text {
	font-size: 1em;
	color: white;
	letter-spacing: 2px;
	margin-bottom: 0.5em;
}
.progress-bar {
	position: relative;
	width: 1000px;
	height: 3em;
	background-color: #111;
	border-radius: 1.5em;
	color: white;
}

.progress-bar::before {
	content: attr(data-label);
	display: flex;
	align-items: center;
	position: absolute;
	left: 0.5em;
	top: 0.5em;
	bottom: 0.5em;
	width: calc(var(--width, 0) * 1%);
	min-width: 2rem;
	max-width: calc(100% - 1em);
	background-color:green;
	border-radius: 1em;
	padding: 1em;
}
