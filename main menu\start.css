body {
    margin: 0;
    background-color: #000;
    font-family: 'Press Start 2P', cursive;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    overflow: hidden;
}

#mainMenu {
    text-align: center;
}

.menu-buttons {
    display: flex;
    flex-direction: column;
    gap: 20px;
    align-items: center;
}

.menu-button {
    width: 1000px;
    height: 100px; /* Set explicit height for your button image */
    padding: 0; /* Remove padding since image will fill */
    background-color: transparent;
    color: white;
    border: none;
    font-size: 18px;
    letter-spacing: 3px;
    cursor: pointer;
    position: relative;
    text-transform: uppercase;
    background-image: url('BAR.png');
    background-size: 100% 100%; /* Stretch image to fill button */
    background-repeat: no-repeat;
    background-position: center;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: transform 0.2s, filter 0.2s;
    font-family: 'Press Start 2P', cursive;
}

/* Remove all pseudo-elements */
.menu-button::before,
.menu-button::after {
    display: none;
}

.menu-button:hover {
    transform: scale(1.03);
    filter: brightness(1.2) drop-shadow(0 0 5px rgba(255, 165, 0, 0.7));
}

.menu-button:active {
    transform: scale(0.98);
    filter: brightness(0.9);
}