const progressBar = document.getElementsByClassName('progress-bar')[0];

let interval = setInterval(() => {
  const computedStyle = getComputedStyle(progressBar);
  let width = parseFloat(computedStyle.getPropertyValue('--width')) || 0;

  if (width >= 100) {
    clearInterval(interval);
    window.location.href = "game.html"; // Redirect to game.html
  } else {
    progressBar.style.setProperty('--width', width + 0.2); // Speed of loading
  }
}, 10); // Adjust interval time for desired speed
